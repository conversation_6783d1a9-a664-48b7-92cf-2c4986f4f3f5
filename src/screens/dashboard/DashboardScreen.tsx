import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ActivityIndicator,
  Animated,
  RefreshControl,
} from 'react-native';
import LinearGradient from 'expo-linear-gradient';

// Importar estilos
import { colors } from '@styles/colors';
import { typography } from '@styles/typography';
import { spacing, borderRadius, animation } from '@styles/spacing';

// Importar componentes modernos
import { ModernCard, MetricCard, ActionCard, LoadingSpinner } from '@components/ui';
import { Icon } from '@components/ui/Icon';

// Importar datos mock
import { mockApi, mockBusinessMetrics, mockClients, mockServices } from '@services/mockData';
import { BusinessMetrics, Client, Service } from '@types';

interface DashboardScreenProps {
  navigation: any;
}

const DashboardScreen: React.FC<DashboardScreenProps> = ({ navigation }) => {
  const [metrics, setMetrics] = useState<BusinessMetrics | null>(null);
  const [recentClients, setRecentClients] = useState<Client[]>([]);
  const [todayServices, setTodayServices] = useState<Service[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  
  // Animaciones
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;

  useEffect(() => {
    loadDashboardData();
    
    // Animación de entrada
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: animation.duration.normal,
        useNativeDriver: true,
      }),
      Animated.spring(slideAnim, {
        toValue: 0,
        ...animation.spring.gentle,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      // Cargar métricas del negocio
      const metricsResponse = await mockApi.getBusinessMetrics({
        start: '2024-06-01T00:00:00Z',
        end: '2024-06-30T23:59:59Z',
      });

      if (metricsResponse.success) {
        setMetrics(metricsResponse.data);
      }

      // Simular clientes recientes y servicios de hoy
      setRecentClients(mockClients.slice(0, 3));
      setTodayServices(mockServices.slice(0, 2));

    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setIsLoading(false);
      if (refreshing) {
        setRefreshing(false);
      }
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-MX', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    loadDashboardData();
  }, []);

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <LoadingSpinner size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Cargando dashboard...</Text>
        </View>
      </SafeAreaView>
    );
  }


  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={[colors.background, colors.surfaceSecondary]}
        style={styles.gradientBackground}
      />
      <ScrollView 
        style={styles.scrollView} 
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={colors.primary}
          />
        }
      >
        {/* Header de bienvenida */}
        <Animated.View 
          style={[
            styles.header,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <View style={styles.greetingContainer}>
            <Text style={styles.greeting}>¡Buenas tardes, María!</Text>
            <Icon name="sun" family="feather" size={28} color={colors.primary} style={styles.greetingIcon} />
          </View>
          <Text style={styles.subtitle}>
            Bienvenida a tu salón
          </Text>
        </Animated.View>

        {/* Resumen de Hoy */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Resumen de Hoy</Text>
          <View style={styles.metricsGrid}>
            <MetricCard
              label="Ingresos"
              value="$1,250"
              change="+12.5%"
              changeType="positive"
              icon="money"
              color={colors.success}
            />
            <MetricCard
              label="Servicios"
              value="8"
              change="5 nuevos"
              changeType="positive"
              icon="services"
              color={colors.primary}
            />
            <MetricCard
              label="Clientes"
              value="5"
              change="2 nuevos"
              changeType="positive"
              icon="clients"
              color={colors.secondary}
            />
          </View>
        </View>

        {/* Acciones rápidas */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Acciones Rápidas</Text>
          <View style={styles.actionsContainer}>
            <ActionCard
              title="Iniciar Proceso"
              description="Workflow completo guiado paso a paso"
              icon="zoomIn"
              color={colors.primary}
              onPress={() => navigation.navigate('WorkflowSelection')}
            />
            <ActionCard
              title="Análisis IA"
              description="Analizar cabello con inteligencia artificial"
              icon="camera"
              color={colors.secondary}
              onPress={() => navigation.navigate('Analysis')}
            />
            <ActionCard
              title="Nuevo Cliente"
              description="Agregar un nuevo cliente al sistema"
              icon="userPlus"
              color={colors.accent}
              onPress={() => navigation.navigate('ClientEdit')}
            />
            <ActionCard
              title="Formulación Profesional"
              description="Proceso completo de formulación asistida por IA"
              icon="droplet"
              color={colors.gradients.sunset[0]}
              onPress={() => navigation.navigate('FormulationWizard')}
            />
            <ActionCard
              title="Catálogo de Productos"
              description="Gestionar inventario y productos"
              icon="product"
              color={colors.info}
              onPress={() => navigation.navigate('ProductCatalog')}
            />
          </View>
        </View>

        {/* Próximas Citas */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Próximas Citas</Text>
            <TouchableOpacity onPress={() => navigation.navigate('Calendar')}>
              <Text style={styles.seeAllText}>Ver todas</Text>
            </TouchableOpacity>
          </View>

          <ModernCard variant="minimal" padding={16}>
            <View style={styles.appointmentItem}>
              <View style={styles.appointmentTime}>
                <Text style={styles.timeText}>10:00</Text>
                <Text style={styles.ampmText}>AM</Text>
              </View>
              <View style={styles.appointmentDetails}>
                <Text style={styles.clientName}>Laura Martínez</Text>
                <View style={styles.serviceRow}>
                  <Icon name="droplet" family="feather" size={14} color={colors.text.secondary} />
                  <Text style={styles.serviceDescription}>Balayage + Corte</Text>
                </View>
              </View>
              <View style={styles.appointmentStatus}>
                <View style={[styles.statusDot, { backgroundColor: colors.success }]} />
              </View>
            </View>
          </ModernCard>

          <ModernCard variant="minimal" padding={16}>
            <View style={styles.appointmentItem}>
              <View style={styles.appointmentTime}>
                <Text style={styles.timeText}>11:30</Text>
                <Text style={styles.ampmText}>AM</Text>
              </View>
              <View style={styles.appointmentDetails}>
                <Text style={styles.clientName}>Ana García</Text>
                <View style={styles.serviceRow}>
                  <Icon name="droplet" family="feather" size={14} color={colors.text.secondary} />
                  <Text style={styles.serviceDescription}>Color + Peinado</Text>
                </View>
              </View>
              <View style={styles.appointmentStatus}>
                <View style={[styles.statusDot, { backgroundColor: colors.warning }]} />
              </View>
            </View>
          </ModernCard>
        </View>

        {/* Clientes recientes */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Clientes Recientes</Text>
            <TouchableOpacity onPress={() => navigation.navigate('Clients')}>
              <Text style={styles.seeAllText}>Ver todos</Text>
            </TouchableOpacity>
          </View>
          
          {recentClients.map((client) => (
            <TouchableOpacity 
              key={client.id} 
              style={styles.clientCard}
              onPress={() => navigation.navigate('ClientDetail', { clientId: client.id })}
            >
              <LinearGradient
                colors={client.vip_status ? colors.gradients.premium : colors.gradients.primary}
                style={styles.clientAvatar}
              >
                <Text style={styles.clientAvatarText}>
                  {client.name.charAt(0).toUpperCase()}
                </Text>
              </LinearGradient>
              <View style={styles.clientInfo}>
                <Text style={styles.clientName}>{client.name}</Text>
                <Text style={styles.clientLastVisit}>
                  Última visita: {new Date(client.last_visit || '').toLocaleDateString('es-ES')}
                </Text>
              </View>
              <View style={styles.clientStats}>
                <Text style={styles.clientSpent}>
                  {formatCurrency(client.total_spent)}
                </Text>
                {client.vip_status && (
                  <View style={styles.vipBadge}>
                    <Icon name="crown" family="feather" size={12} color={colors.vip} />
                    <Text style={styles.vipText}>VIP</Text>
                  </View>
                )}
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  
  gradientBackground: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  loadingText: {
    ...typography.styles.body,
    color: colors.text.secondary,
    marginTop: 16,
  },
  
  scrollView: {
    flex: 1,
  },
  
  header: {
    padding: spacing.lg,
    paddingTop: spacing.md,
  },
  
  greetingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },

  greeting: {
    ...typography.styles.h2,
    color: colors.text.primary,
  },
  
  greetingIcon: {
    marginLeft: spacing.sm,
  },

  subtitle: {
    ...typography.styles.body,
    color: colors.text.secondary,
  },
  
  section: {
    paddingHorizontal: spacing.screenPadding,
    paddingVertical: spacing.md,
  },

  sectionTitle: {
    ...typography.styles.h4,
    color: colors.text.primary,
    marginBottom: spacing.md,
  },

  // Métricas Grid
  metricsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: spacing.sm,
  },

  // Acciones Container
  actionsContainer: {
    gap: spacing.sm,
  },

  // Próximas Citas
  appointmentItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  appointmentTime: {
    alignItems: 'center',
    marginRight: spacing.md,
    minWidth: 60,
  },

  timeText: {
    ...typography.styles.body,
    fontWeight: typography.fontWeight.semibold,
    color: colors.primary,
  },

  ampmText: {
    ...typography.styles.caption,
    color: colors.text.tertiary,
    fontWeight: typography.fontWeight.medium,
  },

  appointmentDetails: {
    flex: 1,
  },

  clientName: {
    ...typography.styles.body,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  
  serviceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },

  serviceDescription: {
    ...typography.styles.bodySmall,
    color: colors.text.secondary,
  },

  appointmentStatus: {
    alignItems: 'center',
    justifyContent: 'center',
  },

  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },

  seeAllText: {
    ...typography.styles.linkSmall,
    color: colors.primary,
  },
  
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  
  metricCard: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 16,
    width: '48%',
    marginBottom: 12,
    shadowColor: colors.shadow.light,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  
  metricValue: {
    ...typography.contexts.dashboard.metric,
    color: colors.text.primary,
    marginBottom: 4,
  },
  
  metricLabel: {
    ...typography.contexts.dashboard.metricLabel,
    color: colors.text.secondary,
    marginBottom: 8,
  },
  
  metricBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  
  metricBadgeText: {
    ...typography.styles.badge,
  },
  
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  
  quickAction: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginHorizontal: 4,
  },
  
  quickActionIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  
  quickActionText: {
    ...typography.styles.buttonSmall,
    color: colors.white,
    textAlign: 'center',
  },
  
  serviceCard: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: colors.shadow.light,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  
  serviceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  
  serviceName: {
    ...typography.styles.h6,
    color: colors.text.primary,
  },
  
  serviceTime: {
    ...typography.styles.caption,
    color: colors.text.secondary,
  },
  
  serviceType: {
    ...typography.styles.bodySmall,
    color: colors.text.secondary,
    marginBottom: 4,
  },
  
  servicePrice: {
    ...typography.styles.currency,
    color: colors.success,
  },
  
  clientCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: colors.shadow.light,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  
  clientAvatar: {
    width: 48,
    height: 48,
    borderRadius: borderRadius.avatar,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
    overflow: 'hidden',
  },
  
  clientAvatarText: {
    ...typography.styles.h5,
    color: colors.primary,
  },
  
  clientInfo: {
    flex: 1,
  },
  
  clientName: {
    ...typography.styles.h6,
    color: colors.text.primary,
    marginBottom: 4,
  },
  
  clientLastVisit: {
    ...typography.styles.caption,
    color: colors.text.secondary,
  },
  
  clientStats: {
    alignItems: 'flex-end',
  },
  
  clientSpent: {
    ...typography.styles.currency,
    color: colors.text.primary,
    marginBottom: 4,
  },
  
  vipBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
    backgroundColor: colors.gradients.premium[0] + '20',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.xs,
  },
  
  vipText: {
    ...typography.styles.badge,
    color: colors.vip,
  },
  
  debugText: {
    fontSize: 12,
    color: colors.error,
    fontWeight: 'bold',
    marginTop: 8,
    backgroundColor: colors.warning + '20',
    padding: 8,
    borderRadius: 4,
  },

  simpleMetrics: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },

  simpleMetricCard: {
    flex: 1,
    backgroundColor: colors.surface,
    padding: 16,
    marginHorizontal: 4,
    borderRadius: 8,
    alignItems: 'center',
    shadowColor: colors.shadow.light,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },

  simpleMetricValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text.primary,
    marginBottom: 4,
  },

  simpleMetricLabel: {
    fontSize: 12,
    color: colors.text.secondary,
    textAlign: 'center',
  },

  emptyState: {
    alignItems: 'center',
    padding: 32,
    backgroundColor: colors.surface,
    borderRadius: 12,
  },
  
  emptyStateIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  
  emptyStateText: {
    ...typography.styles.body,
    color: colors.text.secondary,
    textAlign: 'center',
  },
});

export default DashboardScreen;
