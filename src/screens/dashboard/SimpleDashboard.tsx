import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  Animated,
} from 'react-native';

// Importar estilos
import { colors } from '@styles/colors';
import { GlassCard, AnimatedCounter } from '@components/ui';

interface SimpleDashboardProps {
  navigation: any;
}

const SimpleDashboard: React.FC<SimpleDashboardProps> = ({ navigation }) => {
  console.log('🎯 SimpleDashboard renderizando...');
  
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnims = useRef([
    new Animated.Value(30),
    new Animated.Value(30),
    new Animated.Value(30),
  ]).current;
  
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Buenos días';
    if (hour < 18) return 'Buenas tardes';
    return 'Buenas noches';
  };
  
  useEffect(() => {
    // Fade in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
    
    // Staggered slide animations
    const animations = slideAnims.map((anim, index) => 
      Animated.spring(anim, {
        toValue: 0,
        tension: 50,
        friction: 8,
        delay: index * 100,
        useNativeDriver: true,
      })
    );
    
    Animated.stagger(100, animations).start();
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Header Mejorado */}
        <Animated.View style={[styles.header, { opacity: fadeAnim }]}>
          <View style={styles.headerContent}>
            <View>
              <Text style={styles.title}>¡{getGreeting()}, María! 👋</Text>
              <Text style={styles.subtitle}>Bienvenida a tu salón</Text>
            </View>
            <View style={styles.profileSection}>
              <View style={styles.avatar}>
                <Text style={styles.avatarText}>M</Text>
              </View>
            </View>
          </View>
        </Animated.View>

        {/* Métricas */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Resumen de Hoy</Text>
          
          <View style={styles.metricsContainer}>
            <Animated.View
              style={[{
                opacity: fadeAnim,
                transform: [{ translateY: slideAnims[0] }],
              }]}
            >
              <GlassCard style={[styles.metricCard, styles.modernMetricCard]}>
                <AnimatedCounter
                  value={1250}
                  prefix="$"
                  style={styles.metricNumber}
                  formatter={(value) => value.toLocaleString()}
                />
                <Text style={styles.metricLabel}>Ingresos</Text>
                <Text style={styles.metricTrend}>+12% ↗️</Text>
              </GlassCard>
            </Animated.View>

            <Animated.View
              style={[{
                opacity: fadeAnim,
                transform: [{ translateY: slideAnims[1] }],
              }]}
            >
              <GlassCard style={[styles.metricCard, styles.modernMetricCard]}>
                <AnimatedCounter
                  value={8}
                  style={styles.metricNumber}
                />
                <Text style={styles.metricLabel}>Servicios</Text>
                <Text style={styles.metricTrend}>+2 ✂️</Text>
              </GlassCard>
            </Animated.View>
            
            <Animated.View
              style={[{
                opacity: fadeAnim,
                transform: [{ translateY: slideAnims[2] }],
              }]}
            >
              <GlassCard style={[styles.metricCard, styles.modernMetricCard]}>
                <AnimatedCounter
                  value={5}
                  style={styles.metricNumber}
                />
                <Text style={styles.metricLabel}>Clientes</Text>
                <Text style={styles.metricTrend}>+1 👥</Text>
              </GlassCard>
            </Animated.View>
          </View>
        </View>

        {/* Próximas citas mejoradas */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Próximas Citas</Text>
            <TouchableOpacity>
              <Text style={styles.sectionAction}>Ver todas</Text>
            </TouchableOpacity>
          </View>

          <GlassCard style={styles.appointmentCard}>
            <View style={styles.appointmentHeader}>
              <View style={styles.timeContainer}>
                <Text style={styles.appointmentTime}>10:00</Text>
                <Text style={styles.appointmentPeriod}>AM</Text>
              </View>
              <View style={styles.appointmentInfo}>
                <Text style={styles.appointmentClient}>Laura Martínez</Text>
                <Text style={styles.appointmentService}>💇‍♀️ Balayage + Corte</Text>
              </View>
              <View style={styles.appointmentStatus}>
                <View style={[styles.statusDot, { backgroundColor: colors.accent }]} />
              </View>
            </View>
          </GlassCard>

          <GlassCard style={styles.appointmentCard}>
            <View style={styles.appointmentHeader}>
              <View style={styles.timeContainer}>
                <Text style={styles.appointmentTime}>11:30</Text>
                <Text style={styles.appointmentPeriod}>AM</Text>
              </View>
              <View style={styles.appointmentInfo}>
                <Text style={styles.appointmentClient}>Ana García</Text>
                <Text style={styles.appointmentService}>🎨 Color + Peinado</Text>
              </View>
              <View style={styles.appointmentStatus}>
                <View style={[styles.statusDot, { backgroundColor: colors.warning }]} />
              </View>
            </View>
          </GlassCard>
        </View>

        {/* Acciones rápidas */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Acciones Rápidas</Text>
          
          <View style={styles.actionsContainer}>
            <TouchableOpacity
              onPress={() => navigation.navigate('NewService')}
              activeOpacity={0.7}
            >
              <GlassCard style={[styles.actionButton, styles.modernActionButton]}>
                <Text style={styles.actionIcon}>✂️</Text>
                <Text style={styles.actionText}>Nuevo Servicio</Text>
                <Text style={styles.actionSubtext}>Crear servicio</Text>
              </GlassCard>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => navigation.navigate('CameraCapture', { clientId: 'client-1' })}
              activeOpacity={0.7}
            >
              <GlassCard style={[styles.actionButton, styles.modernActionButton]}>
                <Text style={styles.actionIcon}>📸</Text>
                <Text style={styles.actionText}>Análisis IA</Text>
                <Text style={styles.actionSubtext}>Capturar foto</Text>
              </GlassCard>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => navigation.navigate('ClientEdit')}
              activeOpacity={0.7}
            >
              <GlassCard style={[styles.actionButton, styles.modernActionButton]}>
                <Text style={styles.actionIcon}>👤</Text>
                <Text style={styles.actionText}>Nuevo Cliente</Text>
                <Text style={styles.actionSubtext}>Agregar cliente</Text>
              </GlassCard>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.pearl, // Fondo moderno pearl white
  },
  
  scrollView: {
    flex: 1,
    padding: 16,
  },
  
  header: {
    marginBottom: 32,
  },

  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  profileSection: {
    alignItems: 'center',
  },

  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors.shadow.colored,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },

  avatarText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.white,
  },

  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: colors.text.primary,
    marginBottom: 8,
  },

  subtitle: {
    fontSize: 16,
    color: colors.text.secondary,
  },
  
  section: {
    marginBottom: 32,
  },

  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },

  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text.primary,
  },

  sectionAction: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.primary,
  },
  
  metricsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  
  metricCard: {
    flex: 1,
    padding: 16,
    marginHorizontal: 4,
    alignItems: 'center',
  },
  
  metricNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: 4,
  },
  
  metricLabel: {
    fontSize: 14,
    color: colors.text.secondary,
    textAlign: 'center',
  },
  
  appointmentCard: {
    padding: 20,
    marginBottom: 16,
  },

  appointmentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  timeContainer: {
    alignItems: 'center',
    marginRight: 16,
    minWidth: 60,
  },

  appointmentTime: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.primary,
    lineHeight: 24,
  },

  appointmentPeriod: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.text.secondary,
    marginTop: 2,
  },

  appointmentInfo: {
    flex: 1,
  },

  appointmentClient: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 4,
  },

  appointmentService: {
    fontSize: 14,
    color: colors.text.secondary,
  },

  appointmentStatus: {
    alignItems: 'center',
    justifyContent: 'center',
  },

  statusDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  
  actionButton: {
    flex: 1,
    padding: 16,
    marginHorizontal: 4,
    alignItems: 'center',
  },
  
  actionIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  
  actionText: {
    fontSize: 12,
    color: colors.text.primary,
    textAlign: 'center',
    fontWeight: '500',
  },

  // Estilos modernos tipo Revolut/Booksy
  modernMetricCard: {
    backgroundColor: colors.surface,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: colors.border,
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },

  metricTrend: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.success,
    marginTop: 4,
  },

  modernActionButton: {
    backgroundColor: colors.surface,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: colors.border,
    padding: 20,
    alignItems: 'center',
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },

  actionSubtext: {
    fontSize: 10,
    color: colors.text.secondary,
    textAlign: 'center',
    marginTop: 2,
  },

});

export default SimpleDashboard;
