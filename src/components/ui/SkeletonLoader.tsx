import React, { useRef, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ViewStyle,
  Animated,
  Dimensions,
} from 'react-native';
import LinearGradient from 'expo-linear-gradient';
import { colors } from '@styles/colors';
import { spacing, borderRadius, animation } from '@styles/spacing';
import { theme } from '@styles';

const { width: screenWidth } = Dimensions.get('window');

interface SkeletonLoaderProps {
  width?: number | string;
  height?: number;
  borderRadius?: number;
  style?: ViewStyle;
  variant?: 'text' | 'title' | 'card' | 'avatar' | 'button';
}

const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({
  width,
  height,
  borderRadius: customBorderRadius,
  style,
  variant = 'text',
}) => {
  const shimmerAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.loop(
      Animated.timing(shimmerAnim, {
        toValue: 1,
        duration: 1500,
        useNativeDriver: true,
      }),
    ).start();
  }, []);

  const getVariantStyles = (): ViewStyle => {
    switch (variant) {
      case 'title':
        return {
          width: width || '60%',
          height: height || 24,
          borderRadius: customBorderRadius || borderRadius.xs,
        };
      case 'text':
        return {
          width: width || '100%',
          height: height || 16,
          borderRadius: customBorderRadius || borderRadius.xs,
        };
      case 'card':
        return {
          width: width || '100%',
          height: height || 120,
          borderRadius: customBorderRadius || borderRadius.card,
        };
      case 'avatar':
        return {
          width: width || 48,
          height: height || 48,
          borderRadius: customBorderRadius || borderRadius.avatar,
        };
      case 'button':
        return {
          width: width || 120,
          height: height || 48,
          borderRadius: customBorderRadius || borderRadius.button,
        };
      default:
        return {
          width: width || '100%',
          height: height || 16,
          borderRadius: customBorderRadius || borderRadius.xs,
        };
    }
  };

  const translateX = shimmerAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [-screenWidth, screenWidth],
  });

  return (
    <View style={[styles.container, getVariantStyles(), style]}>
      <Animated.View
        style={[
          styles.shimmer,
          {
            transform: [{ translateX }],
          },
        ]}
      >
        <LinearGradient
          colors={[
            'transparent',
            colors.glass.white,
            colors.glass.whiteStrong,
            colors.glass.white,
            'transparent',
          ]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.gradient}
        />
      </Animated.View>
    </View>
  );
};

// Componente para grupos de skeleton
interface SkeletonGroupProps {
  count?: number;
  variant?: 'text' | 'title' | 'card' | 'avatar' | 'button';
  style?: ViewStyle;
  itemStyle?: ViewStyle;
}

export const SkeletonGroup: React.FC<SkeletonGroupProps> = ({
  count = 3,
  variant = 'text',
  style,
  itemStyle,
}) => {
  return (
    <View style={[styles.group, style]}>
      {Array.from({ length: count }).map((_, index) => (
        <SkeletonLoader
          key={index}
          variant={variant}
          style={[
            itemStyle,
            index < count - 1 && { marginBottom: spacing.sm },
          ]}
        />
      ))}
    </View>
  );
};

// Componente para skeleton de tarjeta completa
export const SkeletonCard: React.FC<{ style?: ViewStyle }> = ({ style }) => {
  return (
    <View style={[styles.card, style]}>
      <View style={styles.cardHeader}>
        <SkeletonLoader variant="avatar" />
        <View style={styles.cardHeaderText}>
          <SkeletonLoader variant="title" width="70%" />
          <SkeletonLoader variant="text" width="50%" style={{ marginTop: spacing.xs }} />
        </View>
      </View>
      <SkeletonGroup count={3} style={{ marginTop: spacing.md }} />
      <SkeletonLoader variant="button" style={{ marginTop: spacing.md, alignSelf: 'flex-start' }} />
    </View>
  );
};

// Componente para skeleton de lista
export const SkeletonList: React.FC<{ count?: number; style?: ViewStyle }> = ({ 
  count = 5, 
  style 
}) => {
  return (
    <View style={style}>
      {Array.from({ length: count }).map((_, index) => (
        <View key={index} style={styles.listItem}>
          <SkeletonLoader variant="avatar" />
          <View style={styles.listItemContent}>
            <SkeletonLoader variant="title" width="80%" />
            <SkeletonLoader variant="text" width="60%" style={{ marginTop: spacing.xs }} />
          </View>
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.gray[100],
    overflow: 'hidden',
  },
  
  shimmer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  
  gradient: {
    flex: 1,
    width: screenWidth * 2,
  },
  
  group: {
    // Estilos para grupo
  },
  
  card: {
    backgroundColor: colors.surface,
    padding: spacing.lg,
    borderRadius: borderRadius.card,
    ...theme.shadows.sm,
  },
  
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  cardHeaderText: {
    flex: 1,
    marginLeft: spacing.md,
  },
  
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: colors.surface,
    marginBottom: spacing.sm,
    borderRadius: borderRadius.md,
  },
  
  listItemContent: {
    flex: 1,
    marginLeft: spacing.md,
  },
});

export default SkeletonLoader;