import React, { useRef, useEffect } from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ActivityIndicator,
  View,
  Animated,
  Platform,
} from 'react-native';
import LinearGradient from 'expo-linear-gradient';
import { colors } from '@styles/colors';
import { spacing, borderRadius, animation } from '@styles/spacing';
import { typography } from '@styles/typography';
import { theme } from '@styles';
import { Icon, IconFamily } from './Icon';

interface ModernButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'gradient' | 'neumorphic';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  icon?: string;
  iconFamily?: IconFamily;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  haptic?: boolean;
}

const ModernButton: React.FC<ModernButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  icon,
  iconFamily = 'feather',
  iconPosition = 'left',
  fullWidth = false,
  style,
  textStyle,
  haptic = true,
}) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;
  const getButtonStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      ...styles.button,
      ...styles[`button_${variant}`],
      ...styles[`button_${size}`],
    };

    if (fullWidth) {
      baseStyle.width = '100%';
    }

    if (disabled) {
      baseStyle.opacity = 0.5;
    }

    return baseStyle;
  };

  const getTextStyle = (): TextStyle => {
    return {
      ...styles.buttonText,
      ...styles[`buttonText_${variant}`],
      ...styles[`buttonText_${size}`],
    };
  };

  const handlePressIn = () => {
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 0.97,
        useNativeDriver: true,
        ...animation.spring.gentle,
      }),
      Animated.timing(opacityAnim, {
        toValue: 0.8,
        duration: animation.duration.fast,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handlePressOut = () => {
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: true,
        ...animation.spring.wobbly,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: animation.duration.fast,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const renderIcon = () => {
    if (!icon || loading) return null;
    
    const iconColor = variant === 'outline' || variant === 'ghost' 
      ? colors.primary 
      : variant === 'neumorphic'
      ? colors.text.primary
      : colors.white;
    
    const iconSize = size === 'small' ? 16 : size === 'large' ? 22 : 18;
    
    return (
      <Icon
        name={icon}
        family={iconFamily}
        size={iconSize}
        color={iconColor}
        style={iconPosition === 'left' ? styles.iconLeft : styles.iconRight}
      />
    );
  };

  const renderContent = () => {
    if (loading) {
      return (
        <ActivityIndicator
          size="small"
          color={
            variant === 'outline' || variant === 'ghost' || variant === 'neumorphic'
              ? colors.primary
              : colors.white
          }
        />
      );
    }

    return (
      <View style={styles.contentContainer}>
        {iconPosition === 'left' && renderIcon()}
        <Text style={[getTextStyle(), textStyle]}>{title}</Text>
        {iconPosition === 'right' && renderIcon()}
      </View>
    );
  };

  const buttonContent = renderContent();

  if (variant === 'gradient') {
    return (
      <Animated.View
        style={[
          { transform: [{ scale: scaleAnim }], opacity: opacityAnim },
          fullWidth && { width: '100%' },
        ]}
      >
        <TouchableOpacity
          onPress={onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          disabled={disabled || loading}
          activeOpacity={1}
        >
          <LinearGradient
            colors={disabled ? [colors.gray[400], colors.gray[500]] : colors.gradients.primary}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={[getButtonStyle(), style]}
          >
            {buttonContent}
          </LinearGradient>
        </TouchableOpacity>
      </Animated.View>
    );
  }

  return (
    <Animated.View
      style={[
        { transform: [{ scale: scaleAnim }], opacity: opacityAnim },
        fullWidth && { width: '100%' },
      ]}
    >
      <TouchableOpacity
        style={[getButtonStyle(), style]}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled || loading}
        activeOpacity={1}
      >
        {buttonContent}
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  button: {
    borderRadius: borderRadius.button,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    ...theme.shadows.md,
  },

  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },

  // Variantes modernas y minimalistas
  button_primary: {
    backgroundColor: colors.primary,
    ...theme.shadows.primary,
  },

  button_secondary: {
    backgroundColor: colors.secondary,
    ...theme.shadows.secondary,
  },

  button_outline: {
    backgroundColor: 'transparent',
    borderWidth: 1.5,
    borderColor: colors.primary,
    ...theme.shadows.none,
  },

  button_ghost: {
    backgroundColor: colors.glass.white,
    backdropFilter: 'blur(10px)',
    ...theme.shadows.none,
  },

  button_gradient: {
    backgroundColor: colors.primary,
    ...theme.shadows.lg,
    shadowColor: colors.primary,
  },

  button_neumorphic: {
    backgroundColor: colors.background,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 4, height: 4 },
        shadowOpacity: 0.08,
        shadowRadius: 8,
      },
      android: {
        elevation: 0,
      },
    }),
  },

  // Tamaños con mejor proporción
  button_small: {
    paddingHorizontal: spacing.buttonPadding.x - 4,
    paddingVertical: spacing.buttonPadding.y - 2,
    minHeight: 40,
  },

  button_medium: {
    paddingHorizontal: spacing.buttonPadding.x,
    paddingVertical: spacing.buttonPadding.y,
    minHeight: 48,
  },

  button_large: {
    paddingHorizontal: spacing.buttonPadding.x + 8,
    paddingVertical: spacing.buttonPadding.y + 4,
    minHeight: 56,
  },

  // Texto base con tipografía mejorada
  buttonText: {
    ...typography.styles.button,
    textAlign: 'center',
  },

  // Texto por variante
  buttonText_primary: {
    color: colors.white,
  },

  buttonText_secondary: {
    color: colors.white,
  },

  buttonText_outline: {
    color: colors.primary,
  },

  buttonText_ghost: {
    color: colors.primary,
  },

  buttonText_gradient: {
    color: colors.white,
  },

  buttonText_neumorphic: {
    color: colors.text.primary,
  },

  // Texto por tamaño
  buttonText_small: {
    ...typography.styles.buttonSmall,
  },

  buttonText_medium: {
    ...typography.styles.button,
  },

  buttonText_large: {
    ...typography.styles.buttonLarge,
  },

  iconLeft: {
    marginRight: spacing.xs,
  },

  iconRight: {
    marginLeft: spacing.xs,
  },
});

export default ModernButton;
