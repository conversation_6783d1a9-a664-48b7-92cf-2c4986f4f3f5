import React, { useRef, useEffect } from 'react';
import { View, StyleSheet, ViewStyle, Platform, Animated } from 'react-native';
import { BlurView } from 'expo-blur';
import LinearGradient from 'expo-linear-gradient';
import { colors } from '@styles/colors';
import { spacing, borderRadius, animation } from '@styles/spacing';
import { theme } from '@styles';

interface GlassCardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  intensity?: number;
  tint?: 'light' | 'dark' | 'default';
  variant?: 'default' | 'elevated' | 'minimal' | 'premium' | 'gradient';
  padding?: number;
  animated?: boolean;
  borderGradient?: boolean;
}

const GlassCard: React.FC<GlassCardProps> = ({
  children,
  style,
  intensity = 0.8,
  tint = 'light',
  variant = 'default',
  padding = spacing.cardPadding,
  animated = false,
  borderGradient = false,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;

  useEffect(() => {
    if (animated) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: animation.duration.normal,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          ...animation.spring.gentle,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      fadeAnim.setValue(1);
      scaleAnim.setValue(1);
    }
  }, [animated]);
  const getVariantStyles = () => {
    switch (variant) {
      case 'elevated':
        return styles.elevated;
      case 'minimal':
        return styles.minimal;
      case 'premium':
        return styles.premium;
      case 'gradient':
        return styles.gradient;
      default:
        return styles.default;
    }
  };

  const renderBorder = () => {
    if (!borderGradient) return null;
    
    return (
      <LinearGradient
        colors={colors.gradients.shimmer}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradientBorder}
      />
    );
  };
  const containerContent = () => {
    if (variant === 'gradient') {
      return (
        <LinearGradient
          colors={colors.gradients.glass}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={[styles.gradientContainer, { padding }]}
        >
          {renderBorder()}
          {children}
        </LinearGradient>
      );
    }

    if (Platform.OS === 'web') {
      return (
        <View style={[styles.glassEffect, { opacity: intensity, padding }]}>
          {renderBorder()}
          {children}
        </View>
      );
    }

    return (
      <BlurView
        intensity={intensity * 100}
        tint={tint}
        style={styles.blurContainer}
      >
        <View style={[styles.glassOverlay, { padding }]}>
          {renderBorder()}
          {children}
        </View>
      </BlurView>
    );
  };

  return (
    <Animated.View
      style={[
        styles.container,
        getVariantStyles(),
        style,
        animated && {
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
        },
      ]}
    >
      {containerContent()}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: borderRadius.card,
    overflow: 'hidden',
  },

  // Variantes de diseño minimalista
  default: {
    ...theme.shadows.md,
  },

  elevated: {
    ...theme.shadows.lg,
    shadowColor: colors.shadow.colored,
  },

  minimal: {
    ...theme.shadows.sm,
  },

  premium: {
    ...theme.shadows.xl,
    shadowColor: colors.shadow.rose,
  },

  gradient: {
    ...theme.shadows.lg,
    shadowColor: colors.shadow.glow,
  },

  blurContainer: {
    flex: 1,
  },

  glassOverlay: {
    flex: 1,
    backgroundColor: colors.glass.white,
    borderRadius: borderRadius.card,
    borderWidth: 1,
    borderColor: colors.glass.border,
  },

  // Fallback styles for web
  glassEffect: {
    backgroundColor: colors.gradients.card[0],
    borderRadius: borderRadius.card,
    borderWidth: 1,
    borderColor: colors.glass.border,
    backdropFilter: 'blur(20px)',
    WebkitBackdropFilter: 'blur(20px)',
  },
  
  gradientContainer: {
    flex: 1,
    borderRadius: borderRadius.card,
    borderWidth: 1,
    borderColor: colors.glass.border,
  },
  
  gradientBorder: {
    position: 'absolute',
    top: -1,
    left: -1,
    right: -1,
    bottom: -1,
    borderRadius: borderRadius.card,
    opacity: 0.3,
  },
});

export default GlassCard;