import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  TouchableOpacity,
  StatusBar,
  Platform,
  ViewStyle,
} from 'react-native';
import { BlurView } from 'expo-blur';
import LinearGradient from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { colors } from '@styles/colors';
import { typography } from '@styles/typography';
import { spacing, borderRadius, animation } from '@styles/spacing';
import { theme } from '@styles';
import { Icon } from './Icon';

interface AnimatedHeaderProps {
  title: string;
  subtitle?: string;
  scrollY?: Animated.Value;
  onBack?: () => void;
  rightAction?: {
    icon: string;
    onPress: () => void;
  };
  variant?: 'default' | 'transparent' | 'gradient';
  style?: ViewStyle;
}

const AnimatedHeader: React.FC<AnimatedHeaderProps> = ({
  title,
  subtitle,
  scrollY,
  onBack,
  rightAction,
  variant = 'default',
  style,
}) => {
  const insets = useSafeAreaInsets();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const headerHeight = 56 + insets.top;
  const scrollThreshold = 50;

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: animation.duration.normal,
      useNativeDriver: true,
    }).start();
  }, []);

  // Animaciones basadas en scroll
  const headerOpacity = scrollY
    ? scrollY.interpolate({
        inputRange: [0, scrollThreshold],
        outputRange: [0, 1],
        extrapolate: 'clamp',
      })
    : 1;

  const titleScale = scrollY
    ? scrollY.interpolate({
        inputRange: [0, scrollThreshold],
        outputRange: [1.2, 1],
        extrapolate: 'clamp',
      })
    : 1;

  const titleTranslateY = scrollY
    ? scrollY.interpolate({
        inputRange: [0, scrollThreshold],
        outputRange: [20, 0],
        extrapolate: 'clamp',
      })
    : 0;

  const renderBackground = () => {
    if (variant === 'transparent') {
      return (
        <Animated.View
          style={[
            styles.blurContainer,
            {
              opacity: headerOpacity,
            },
          ]}
        >
          <BlurView
            intensity={80}
            tint="light"
            style={StyleSheet.absoluteFillObject}
          />
        </Animated.View>
      );
    }

    if (variant === 'gradient') {
      return (
        <LinearGradient
          colors={colors.gradients.primary}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={StyleSheet.absoluteFillObject}
        />
      );
    }

    return (
      <Animated.View
        style={[
          styles.defaultBackground,
          {
            opacity: headerOpacity,
          },
        ]}
      />
    );
  };

  return (
    <>
      <StatusBar
        barStyle={variant === 'gradient' ? 'light-content' : 'dark-content'}
        backgroundColor="transparent"
        translucent
      />
      <Animated.View
        style={[
          styles.container,
          {
            height: headerHeight,
            paddingTop: insets.top,
            opacity: fadeAnim,
          },
          style,
        ]}
      >
        {renderBackground()}
        
        <View style={styles.content}>
          {onBack && (
            <TouchableOpacity
              onPress={onBack}
              style={styles.backButton}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Icon
                name="arrow-left"
                family="feather"
                size={24}
                color={variant === 'gradient' ? colors.white : colors.text.primary}
              />
            </TouchableOpacity>
          )}
          
          <Animated.View
            style={[
              styles.titleContainer,
              {
                transform: [
                  { scale: titleScale },
                  { translateY: titleTranslateY },
                ],
              },
            ]}
          >
            <Text
              style={[
                styles.title,
                variant === 'gradient' && styles.titleLight,
              ]}
              numberOfLines={1}
            >
              {title}
            </Text>
            {subtitle && (
              <Text
                style={[
                  styles.subtitle,
                  variant === 'gradient' && styles.subtitleLight,
                ]}
                numberOfLines={1}
              >
                {subtitle}
              </Text>
            )}
          </Animated.View>
          
          {rightAction && (
            <TouchableOpacity
              onPress={rightAction.onPress}
              style={styles.rightButton}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Icon
                name={rightAction.icon}
                family="feather"
                size={24}
                color={variant === 'gradient' ? colors.white : colors.text.primary}
              />
            </TouchableOpacity>
          )}
        </View>
        
        {variant !== 'transparent' && (
          <Animated.View
            style={[
              styles.border,
              {
                opacity: headerOpacity,
              },
            ]}
          />
        )}
      </Animated.View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
  },
  
  blurContainer: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: colors.glass.blur,
  },
  
  defaultBackground: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: colors.surface,
  },
  
  content: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
  },
  
  backButton: {
    width: 40,
    height: 40,
    borderRadius: borderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.glass.white,
    ...Platform.select({
      ios: {
        ...theme.shadows.sm,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  
  titleContainer: {
    flex: 1,
    marginHorizontal: spacing.md,
  },
  
  title: {
    ...typography.styles.h5,
    color: colors.text.primary,
    textAlign: 'center',
  },
  
  titleLight: {
    color: colors.white,
  },
  
  subtitle: {
    ...typography.styles.caption,
    color: colors.text.secondary,
    textAlign: 'center',
    marginTop: 2,
  },
  
  subtitleLight: {
    color: colors.white + '80',
  },
  
  rightButton: {
    width: 40,
    height: 40,
    borderRadius: borderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.glass.white,
    ...Platform.select({
      ios: {
        ...theme.shadows.sm,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  
  border: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 1,
    backgroundColor: colors.border.light,
  },
});

export default AnimatedHeader;