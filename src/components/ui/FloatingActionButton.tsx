import React, { useRef, useEffect, useState } from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Platform,
  ViewStyle,
} from 'react-native';
import LinearGradient from 'expo-linear-gradient';
import { colors } from '@styles/colors';
import { spacing, borderRadius, animation } from '@styles/spacing';
import { theme } from '@styles';
import { Icon, IconFamily } from './Icon';

interface FABAction {
  icon: string;
  iconFamily?: IconFamily;
  label?: string;
  color?: string;
  onPress: () => void;
}

interface FloatingActionButtonProps {
  icon: string;
  iconFamily?: IconFamily;
  onPress?: () => void;
  actions?: FABAction[];
  position?: 'bottom-right' | 'bottom-left' | 'bottom-center';
  style?: ViewStyle;
  color?: string;
  gradient?: boolean;
}

const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  icon,
  iconFamily = 'feather',
  onPress,
  actions,
  position = 'bottom-right',
  style,
  color = colors.primary,
  gradient = true,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const expandAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(rotateAnim, {
        toValue: isExpanded ? 1 : 0,
        duration: animation.duration.fast,
        useNativeDriver: true,
      }),
      Animated.timing(expandAnim, {
        toValue: isExpanded ? 1 : 0,
        duration: animation.duration.fast,
        useNativeDriver: true,
      }),
    ]).start();
  }, [isExpanded]);

  const handleMainPress = () => {
    if (actions && actions.length > 0) {
      setIsExpanded(!isExpanded);
    } else if (onPress) {
      onPress();
    }
  };

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.95,
      ...animation.spring.gentle,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      ...animation.spring.wobbly,
      useNativeDriver: true,
    }).start();
  };

  const getPositionStyle = () => {
    switch (position) {
      case 'bottom-left':
        return { left: spacing.lg, bottom: spacing.lg };
      case 'bottom-center':
        return { alignSelf: 'center', bottom: spacing.lg };
      default:
        return { right: spacing.lg, bottom: spacing.lg };
    }
  };

  const rotation = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '45deg'],
  });

  const renderMainButton = () => {
    const buttonContent = (
      <View style={styles.mainButton}>
        <Animated.View style={{ transform: [{ rotate: rotation }] }}>
          <Icon
            name={actions && actions.length > 0 ? 'plus' : icon}
            family={iconFamily}
            size={24}
            color={colors.white}
          />
        </Animated.View>
      </View>
    );

    if (gradient) {
      return (
        <LinearGradient
          colors={[color, colors.gradients.primary[1]]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.gradientButton}
        >
          {buttonContent}
        </LinearGradient>
      );
    }

    return (
      <View style={[styles.solidButton, { backgroundColor: color }]}>
        {buttonContent}
      </View>
    );
  };

  return (
    <View style={[styles.container, getPositionStyle(), style]}>
      {actions && actions.map((action, index) => {
        const translateY = expandAnim.interpolate({
          inputRange: [0, 1],
          outputRange: [0, -(60 * (index + 1))],
        });

        const opacity = expandAnim.interpolate({
          inputRange: [0, 0.5, 1],
          outputRange: [0, 0, 1],
        });

        const scale = expandAnim.interpolate({
          inputRange: [0, 1],
          outputRange: [0.8, 1],
        });

        return (
          <Animated.View
            key={index}
            style={[
              styles.actionContainer,
              {
                transform: [{ translateY }, { scale }],
                opacity,
              },
            ]}
          >
            <TouchableOpacity
              onPress={() => {
                action.onPress();
                setIsExpanded(false);
              }}
              style={[
                styles.actionButton,
                { backgroundColor: action.color || colors.surface },
              ]}
              activeOpacity={0.8}
            >
              <Icon
                name={action.icon}
                family={action.iconFamily || 'feather'}
                size={20}
                color={action.color ? colors.white : colors.text.primary}
              />
            </TouchableOpacity>
          </Animated.View>
        );
      })}
      
      <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
        <TouchableOpacity
          onPress={handleMainPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          activeOpacity={1}
          style={styles.touchable}
        >
          {renderMainButton()}
        </TouchableOpacity>
      </Animated.View>
      
      {isExpanded && (
        <TouchableOpacity
          style={styles.backdrop}
          activeOpacity={1}
          onPress={() => setIsExpanded(false)}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    zIndex: 999,
  },
  
  touchable: {
    borderRadius: borderRadius.full,
  },
  
  mainButton: {
    width: 56,
    height: 56,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  gradientButton: {
    width: 56,
    height: 56,
    borderRadius: borderRadius.full,
    ...theme.shadows.lg,
    shadowColor: colors.shadow.colored,
  },

  solidButton: {
    width: 56,
    height: 56,
    borderRadius: borderRadius.full,
    ...theme.shadows.lg,
  },
  
  actionContainer: {
    position: 'absolute',
    alignItems: 'center',
  },
  
  actionButton: {
    width: 44,
    height: 44,
    borderRadius: borderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.sm,
    ...theme.shadows.md,
  },
  
  backdrop: {
    position: 'absolute',
    top: -1000,
    left: -1000,
    right: -100,
    bottom: -100,
  },
});

export default FloatingActionButton;