import React, { useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TextStyle,
  Animated,
} from 'react-native';
import LinearGradient from 'expo-linear-gradient';
import { colors } from '@styles/colors';
import { spacing, borderRadius, animation } from '@styles/spacing';
import { typography } from '@styles/typography';
import { theme } from '@styles';
import GlassCard from './GlassCard';
import { Icon, getIcon } from './Icon';

interface ModernCardProps {
  children?: React.ReactNode;
  title?: string;
  subtitle?: string;
  onPress?: () => void;
  style?: ViewStyle;
  variant?: 'default' | 'elevated' | 'minimal' | 'premium' | 'gradient';
  padding?: number;
  showBorder?: boolean;
  gradientColors?: string[];
}

const ModernCard: React.FC<ModernCardProps> = ({
  children,
  title,
  subtitle,
  onPress,
  style,
  variant = 'default',
  padding = spacing.cardPadding,
  showBorder = false,
  gradientColors = colors.gradients.card,
}) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const cardStyle: ViewStyle = {
    ...styles.card,
    ...(showBorder && styles.bordered),
  };

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.98,
      useNativeDriver: true,
      ...animation.spring.gentle,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
      ...animation.spring.wobbly,
    }).start();
  };

  const CardContent = () => {
    const content = (
      <>
        {(title || subtitle) && (
          <View style={styles.header}>
            {title && <Text style={styles.title}>{title}</Text>}
            {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
          </View>
        )}
        {children}
      </>
    );

    if (variant === 'gradient') {
      return (
        <LinearGradient
          colors={gradientColors}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={[cardStyle, styles.gradientCard, style]}
        >
          {content}
        </LinearGradient>
      );
    }

    return (
      <GlassCard variant={variant} padding={padding} style={[cardStyle, style]}>
        {content}
      </GlassCard>
    );
  };

  if (onPress) {
    return (
      <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
        <TouchableOpacity 
          onPress={onPress} 
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          activeOpacity={1}
        >
          <CardContent />
        </TouchableOpacity>
      </Animated.View>
    );
  }

  return <CardContent />;
};

// Componente especializado para métricas estilo Revolut
interface MetricCardProps {
  label: string;
  value: string | number;
  change?: string;
  changeType?: 'positive' | 'negative' | 'neutral';
  icon?: keyof typeof import('./Icon').iconMap;
  color?: string;
  style?: ViewStyle;
  animated?: boolean;
}

export const MetricCard: React.FC<MetricCardProps> = ({
  label,
  value,
  change,
  changeType = 'neutral',
  icon,
  color = colors.primary,
  style,
  animated = true,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(20)).current;

  React.useEffect(() => {
    if (animated) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: animation.duration.normal,
          useNativeDriver: true,
        }),
        Animated.spring(slideAnim, {
          toValue: 0,
          useNativeDriver: true,
          ...animation.spring.gentle,
        }),
      ]).start();
    }
  }, []);
  const getChangeColor = () => {
    switch (changeType) {
      case 'positive':
        return colors.success;
      case 'negative':
        return colors.error;
      default:
        return colors.gray[500];
    }
  };

  const cardContent = (
    <ModernCard variant="elevated" padding={spacing.md} style={[styles.metricCard, style]}>
      <View style={styles.metricHeader}>
        {icon && (
          <LinearGradient
            colors={[color + '20', color + '10']}
            style={styles.metricIcon}
          >
            <Icon 
              name={getIcon(icon).name} 
              family={getIcon(icon).family}
              size={16} 
              color={color} 
            />
          </LinearGradient>
        )}
        <Text style={styles.metricLabel}>{label.toUpperCase()}</Text>
      </View>
      
      <Text style={[styles.metricValue, { color }]}>{value}</Text>
      
      {change && (
        <View style={styles.changeContainer}>
          <Icon
            name={changeType === 'positive' ? 'trending-up' : changeType === 'negative' ? 'trending-down' : 'minus'}
            family="feather"
            size={12}
            color={getChangeColor()}
            style={{ marginRight: 4 }}
          />
          <Text style={[styles.changeText, { color: getChangeColor() }]}>
            {change}
          </Text>
        </View>
      )}
    </ModernCard>
  );

  if (animated) {
    return (
      <Animated.View
        style={{
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }],
        }}
      >
        {cardContent}
      </Animated.View>
    );
  }

  return cardContent;
};

// Componente para acciones rápidas estilo Revolut
interface ActionCardProps {
  title: string;
  description: string;
  icon: keyof typeof import('./Icon').iconMap;
  onPress: () => void;
  color?: string;
  style?: ViewStyle;
  showArrow?: boolean;
}

export const ActionCard: React.FC<ActionCardProps> = ({
  title,
  description,
  icon,
  onPress,
  color = colors.primary,
  style,
  showArrow = true,
}) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.96,
      useNativeDriver: true,
      ...animation.spring.gentle,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
      ...animation.spring.wobbly,
    }).start();
  };

  return (
    <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
      <TouchableOpacity 
        onPress={onPress} 
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={1}
      >
        <ModernCard variant="minimal" padding={spacing.lg} style={[styles.actionCard, style]}>
          <View style={styles.actionContent}>
            <LinearGradient
              colors={[color + '15', color + '08']}
              style={styles.actionIcon}
            >
              <Icon 
                name={getIcon(icon).name} 
                family={getIcon(icon).family}
                size={24} 
                color={color} 
              />
            </LinearGradient>
            
            <View style={styles.actionText}>
              <Text style={styles.actionTitle}>{title}</Text>
              <Text style={styles.actionDescription}>{description}</Text>
            </View>
            
            {showArrow && (
              <Icon
                name="chevron-right"
                family="feather"
                size={20}
                color={color}
              />
            )}
          </View>
        </ModernCard>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: spacing.md,
  },
  
  bordered: {
    borderWidth: 1,
    borderColor: colors.glass.border,
  },

  gradientCard: {
    borderRadius: borderRadius.card,
    padding: spacing.cardPadding,
    ...theme.shadows.lg,
  },
  
  header: {
    marginBottom: spacing.sm,
  },
  
  title: {
    ...typography.styles.h5,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  
  subtitle: {
    ...typography.styles.bodySmall,
    color: colors.text.secondary,
  },
  
  // Metric Card Styles
  metricCard: {
    flex: 1,
    marginHorizontal: spacing.xs,
  },
  
  metricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  
  metricIcon: {
    width: 32,
    height: 32,
    borderRadius: borderRadius.sm,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
    overflow: 'hidden',
  },
  
  metricLabel: {
    ...typography.styles.overline,
    color: colors.text.secondary,
    letterSpacing: 1,
  },
  
  metricValue: {
    ...typography.contexts.dashboard.metric,
    marginBottom: spacing.xs,
  },
  
  changeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  changeText: {
    ...typography.styles.caption,
    fontWeight: typography.fontWeight.semibold,
  },
  
  // Action Card Styles
  actionCard: {
    marginBottom: spacing.sm,
  },
  
  actionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: borderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
    overflow: 'hidden',
  },
  
  actionText: {
    flex: 1,
  },
  
  actionTitle: {
    ...typography.styles.body,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: 2,
  },
  
  actionDescription: {
    ...typography.styles.bodySmall,
    color: colors.text.secondary,
  },
});

export default ModernCard;
